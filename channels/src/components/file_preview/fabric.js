import React, {useEffect, useRef} from 'react';
import {fabric} from 'fabric';

type Props = {
  imageUrl: string;                 // رابط الصورة الأصلية
  onSave: (blob: Blob) => void;     // يرجع الصورة المعدلة عند الحفظ
  onClose: () => void;              // إغلاق المحرر
};

const ImageEditor: React.FC<Props> = ({imageUrl, onSave, onClose}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvas = useRef<fabric.Canvas>();

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = new fabric.Canvas(canvasRef.current, {
      isDrawingMode: true, // تفعيل القلم
    });
    fabricCanvas.current = canvas;

    // تحميل الصورة في الخلفية
    fabric.Image.fromURL(imageUrl, (img) => {
      if (!img) return;
      canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
        scaleX: canvas.width! / img.width!,
        scaleY: canvas.height! / img.height!,
      });
    });

    return () => {
      canvas.dispose();
    };
  }, [imageUrl]);

  const handleSave = () => {
    if (fabricCanvas.current) {
      fabricCanvas.current.discardActiveObject();
      fabricCanvas.current.renderAll();

      const dataUrl = fabricCanvas.current.toDataURL({
        format: 'png',
        quality: 1,
      });

      fetch(dataUrl)
        .then((res) => res.blob())
        .then((blob) => onSave(blob));
    }
  };

  return (
    <div style={{textAlign: 'center'}}>
      <canvas ref={canvasRef} width={800} height={500} style={{border: '1px solid #ccc'}}/>
      <div style={{marginTop: '10px'}}>
        <button onClick={() => { fabricCanvas.current!.isDrawingMode = true; }}>✏️ قلم</button>
        <button onClick={() => { fabricCanvas.current!.isDrawingMode = false; }}>🖼️ تحريك</button>
        <button onClick={handleSave}>💾 حفظ</button>
        <button onClick={onClose}>❌ إغلاق</button>
      </div>
    </div>
  );
};

export default ImageEditor;
