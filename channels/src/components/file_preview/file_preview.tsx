// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useState, useRef, useCallback} from 'react';
import type {ReactNode} from 'react';

import type {FileInfo} from '@mattermost/types/files';

import {getFileThumbnailUrl, getFileUrl} from 'mattermost-redux/utils/file_utils';

import FilenameOverlay from 'components/file_attachment/filename_overlay';
import FilePreviewModal from 'components/file_preview_modal';
import RootPortal from 'components/root_portal';

import Constants, {FileTypes, ModalIdentifiers} from 'utils/constants';
import * as Utils from 'utils/utils';

import type {ModalData} from 'types/actions';

import FileProgressPreview from './file_progress_preview';
import MediaEditor from '../custom_status/media_editor';

type UploadInfo = {
    name: string;
    percent?: number;
    type?: string;
}
export type FilePreviewInfo = FileInfo & UploadInfo;

type Props = {
    enableSVGs: boolean;
    onRemove?: (id: string) => void;
    onFileUpdate?: (oldFileInfo: FileInfo, newFile: File) => void;
    fileInfos: FilePreviewInfo[];
    uploadsInProgress?: string[];
    uploadsProgressPercent?: {[clientID: string]: FilePreviewInfo};
    actions: {
        openModal: <P>(modalData: ModalData<P>) => void;
    };
}

const FilePreview: React.FC<Props> = (props) => {
    const {
        enableSVGs = false,
        onRemove,
        onFileUpdate,
        fileInfos = [],
        uploadsInProgress = [],
        uploadsProgressPercent = {},
        actions
    } = props;

    const [isMediaEditMode, setIsMediaEditMode] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [selectedFileInfo, setSelectedFileInfo] = useState<FileInfo | null>(null);
    const [editedFileUrls, setEditedFileUrls] = useState<{[fileId: string]: string}>({});
    const imageEditorRef = useRef(null);

    const handleRemove = useCallback((id: string, e?: React.MouseEvent) => {
        e?.stopPropagation();
        
        // تنظيف URL الملف المحرر إذا كان موجوداً
        if (editedFileUrls[id]) {
            URL.revokeObjectURL(editedFileUrls[id]);
            setEditedFileUrls(prev => {
                const newUrls = {...prev};
                delete newUrls[id];
                return newUrls;
            });
        }
        
        onRemove?.(id);
    }, [onRemove, editedFileUrls]);

    const handleShowMediaPreview = useCallback(async (fileInfo: FileInfo) => {
        const notAllowedExtensions = ['pdf', 'docx', 'doc', 'dotx', 'xlsx', 'xls', 'xlsm', 'pptx', 'ppt', 'ppsx'];
        
        if (notAllowedExtensions.includes(fileInfo.extension)) {
            return;
        }

        // التحقق من أن الملف صورة أو فيديو لفتح المحرر
        const fileType = Utils.getFileType(fileInfo.extension);
        if (fileType === FileTypes.IMAGE || fileType === FileTypes.VIDEO) {
            try {
                // تحويل FileInfo إلى File object
                const fileUrl = getFileUrl(fileInfo.id);
                const response = await fetch(fileUrl);
                const blob = await response.blob();
                const file = new File([blob], fileInfo.name, {
                    type: fileInfo.mime_type || 'application/octet-stream',
                    lastModified: fileInfo.create_at
                });
                
                setSelectedFile(file);
                setSelectedFileInfo(fileInfo);
                setIsMediaEditMode(true);
            } catch (error) {
                console.error('Error loading file for editing:', error);
                // fallback to normal preview
                actions.openModal({
                    modalId: ModalIdentifiers.FILE_PREVIEW_MODAL,
                    dialogType: FilePreviewModal,
                    dialogProps: {
                        fileInfos: [fileInfo],
                        postId: fileInfo.post_id,
                        startIndex: 0,
                    },
                });
            }
        } else {
            // للملفات الأخرى، استخدم المعاينة العادية
            actions.openModal({
                modalId: ModalIdentifiers.FILE_PREVIEW_MODAL,
                dialogType: FilePreviewModal,
                dialogProps: {
                    fileInfos: [fileInfo],
                    postId: fileInfo.post_id,
                    startIndex: 0,
                },
            });
        }
    }, [actions]);

    const onSave = useCallback((editedFile?: File) => {
        if (editedFile && selectedFileInfo) {
            // إنشاء URL للملف المحرر
            const editedFileUrl = URL.createObjectURL(editedFile);
            
            // حفظ URL الملف المحرر
            setEditedFileUrls(prev => ({
                ...prev,
                [selectedFileInfo.id]: editedFileUrl
            }));

            // إشعار المكون الأب بالتحديث
            if (onFileUpdate) {
                onFileUpdate(selectedFileInfo, editedFile);
            }
        }
        setIsMediaEditMode(false);
        setSelectedFile(null);
        setSelectedFileInfo(null);
    }, [selectedFileInfo, onFileUpdate]);

    const onCancel = useCallback(() => {
        setIsMediaEditMode(false);
        setSelectedFile(null);
        setSelectedFileInfo(null);
    }, []);

    const previews: ReactNode[] = [];

    fileInfos.forEach((info) => {
            const type = Utils.getFileType(info.extension);

            let className = 'file-preview post-image__column';
            let previewImage;
            if (type === FileTypes.SVG && enableSVGs) {
                // استخدام URL المحرر إذا كان متوفراً، وإلا استخدم الأصلي
                const imageUrl = editedFileUrls[info.id] || getFileUrl(info.id);
                previewImage = (
                    <img
                        alt={'file preview'}
                        className='post-image normal'
                        src={imageUrl}
                    />
                );
            } else if (type === FileTypes.IMAGE) {
                let imageClassName = 'post-image';

                if ((info.width && info.width < Constants.THUMBNAIL_WIDTH) && (info.height && info.height < Constants.THUMBNAIL_HEIGHT)) {
                    imageClassName += ' small';
                } else {
                    imageClassName += ' normal';
                }

                // استخدام URL المحرر إذا كان متوفراً، وإلا استخدم الأصلي
                let thumbnailUrl = editedFileUrls[info.id];
                if (!thumbnailUrl) {
                    thumbnailUrl = getFileThumbnailUrl(info.id);
                    if (Utils.isGIFImage(info.extension) && !info.has_preview_image) {
                        thumbnailUrl = getFileUrl(info.id);
                    }
                }

                previewImage = (
                    <div
                        className={imageClassName}
                        style={{
                            backgroundImage: `url(${thumbnailUrl})`,
                            backgroundSize: 'cover',
                        }}
                    />
                );
            } else {
                className += ' custom-file';
                previewImage = <div className={'file-icon ' + Utils.getIconClassName(type)}/>;
            }

            previews.push(
                <div
                    key={info.id}
                    className={className}
                    onClick={() => handleShowMediaPreview(info)}
                >
                    <div className='post-image__thumbnail'>
                        {previewImage}
                    </div>
                    <div className='post-image__details'>
                        <div className='post-image__detail_wrapper'>
                            <div className='post-image__detail'>
                                <FilenameOverlay
                                    fileInfo={info}
                                    compactDisplay={false}
                                    canDownload={false}
                                />
                                {info.extension && <span className='post-image__type'>{info.extension.toUpperCase()}</span>}
                                <span className='post-image__size'>{Utils.fileSizeToString(info.size)}</span>
                            </div>
                        </div>
                        <div>
                            {Boolean(onRemove) && (
                                <a
                                    className='file-preview__remove'
                                    onClick={(e) => handleRemove(info.id, e)}
                                >
                                    <i className='icon icon-close'/>
                                </a>
                            )}
                        </div>
                    </div>
                </div>,
            );
        });

        if (uploadsInProgress && uploadsProgressPercent) {
            uploadsInProgress.forEach((clientId) => {
                const fileInfo = uploadsProgressPercent[clientId];
                if (fileInfo) {
                    previews.push(
                        <FileProgressPreview
                            key={clientId}
                            clientId={clientId}
                            fileInfo={fileInfo}
                            handleRemove={handleRemove}
                        />,
                    );
                }
            });
        }

    return (
        <>
            <div className='file-preview__container'>
                {previews}
            </div>
            {selectedFile && isMediaEditMode && (
                <RootPortal>
                    <MediaEditor
                        ref={imageEditorRef}
                        file={selectedFile}
                        onSave={onSave}
                        onCancel={onCancel}
                    />
                </RootPortal>
            )}
        </>
    );
};

export default FilePreview;
